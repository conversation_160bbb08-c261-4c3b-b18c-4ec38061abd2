{"architecture_type": "encoder_decoder_tcn", "model_size": {"total_parameters": 1625829, "trainable_parameters": 1625829, "model_size_mb": 6.202045440673828}, "memory_usage": {"parameter_memory_gb": 0.006056685000658035, "input_memory_gb": 0.00244140625, "activation_memory_gb": 0.0244140625, "gradient_memory_gb": 0.006056685000658035, "optimizer_memory_gb": 0.01211337000131607, "total_training_memory_gb": 0.05108220875263214, "total_inference_memory_gb": 0.032912153750658035}, "input_shape": [8, 1024, 80], "output_shape": [8, 512, 5]}