2025-07-26 20:34:45,497 - INFO - Logging configured
2025-07-26 20:34:45,497 - INFO - Model Architecture Designer initialized
2025-07-26 20:34:45,497 - INFO - === Starting Phase 3: Model Architecture Design ===
2025-07-26 20:34:45,497 - INFO - Starting architecture variant design
2025-07-26 20:34:45,497 - INFO - Creating model variant: lightweight
2025-07-26 20:34:45,538 - INFO - Model configuration saved to: data\processed\phase3\model_configs\lightweight_config.json
2025-07-26 20:34:45,538 - ERROR - Failed to create model variant 'lightweight': [Errno 2] No such file or directory: 'data\\processed\\phase3\\architecture_specs\\lightweight_specs.json'
2025-07-26 20:34:45,539 - INFO - Creating model variant: standard
2025-07-26 20:34:45,551 - INFO - Model configuration saved to: data\processed\phase3\model_configs\standard_config.json
2025-07-26 20:34:45,551 - ERROR - Failed to create model variant 'standard': [Errno 2] No such file or directory: 'data\\processed\\phase3\\architecture_specs\\standard_specs.json'
2025-07-26 20:34:45,551 - INFO - Creating model variant: enhanced
2025-07-26 20:34:45,580 - INFO - Model configuration saved to: data\processed\phase3\model_configs\enhanced_config.json
2025-07-26 20:34:45,581 - ERROR - Failed to create model variant 'enhanced': [Errno 2] No such file or directory: 'data\\processed\\phase3\\architecture_specs\\enhanced_specs.json'
2025-07-26 20:34:45,581 - INFO - Architecture variant design completed. Created: 0, Failed: 3
2025-07-26 20:34:45,581 - ERROR - Phase 3 failed: No successful model variants created
2025-07-26 20:35:07,070 - INFO - Logging configured
2025-07-26 20:35:07,071 - INFO - Model Architecture Designer initialized
2025-07-26 20:35:07,071 - INFO - === Starting Phase 3: Model Architecture Design ===
2025-07-26 20:35:07,071 - INFO - Starting architecture variant design
2025-07-26 20:35:07,071 - INFO - Creating model variant: lightweight
2025-07-26 20:35:07,079 - INFO - Model configuration saved to: data\processed\phase3\model_configs\lightweight_config.json
2025-07-26 20:35:07,079 - INFO - Model variant 'lightweight' created successfully
2025-07-26 20:35:07,079 - INFO - Parameters: 409,973
2025-07-26 20:35:07,080 - INFO - Model size: 1.56 MB
2025-07-26 20:35:07,080 - INFO - Training memory: 0.03 GB
2025-07-26 20:35:07,080 - INFO - Creating model variant: standard
2025-07-26 20:35:07,091 - INFO - Model configuration saved to: data\processed\phase3\model_configs\standard_config.json
2025-07-26 20:35:07,092 - INFO - Model variant 'standard' created successfully
2025-07-26 20:35:07,092 - INFO - Parameters: 1,625,829
2025-07-26 20:35:07,092 - INFO - Model size: 6.20 MB
2025-07-26 20:35:07,092 - INFO - Training memory: 0.05 GB
2025-07-26 20:35:07,093 - INFO - Creating model variant: enhanced
2025-07-26 20:35:07,122 - INFO - Model configuration saved to: data\processed\phase3\model_configs\enhanced_config.json
2025-07-26 20:35:07,123 - INFO - Model variant 'enhanced' created successfully
2025-07-26 20:35:07,123 - INFO - Parameters: 6,475,205
2025-07-26 20:35:07,123 - INFO - Model size: 24.70 MB
2025-07-26 20:35:07,123 - INFO - Training memory: 0.12 GB
2025-07-26 20:35:07,123 - INFO - Architecture variant design completed. Created: 3, Failed: 0
2025-07-26 20:35:07,124 - INFO - Architecture constraints validation passed
2025-07-26 20:35:07,124 - INFO - Architecture constraints validation passed
2025-07-26 20:35:07,125 - INFO - Architecture constraints validation passed
2025-07-26 20:35:07,126 - INFO - Summary report saved to: data\processed\phase3\processing_logs\phase3_summary.json
2025-07-26 20:35:07,126 - INFO - === Phase 3 completed successfully in 0.06 seconds ===
2025-07-26 20:35:07,126 - INFO - Created 3 successful model variants
