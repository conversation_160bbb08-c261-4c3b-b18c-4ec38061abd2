{"file_path": "data\\raw\\ese\\01 Pop\\Summer Time Cinderella\\Summer Time Cinderella.ogg", "filename": "Summer Time Cinderella", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.453, "original_sr": 48000, "original_channels": 2, "original_duration": 101.64, "file_size_mb": 5.56, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 101.64, "sample_count": 2241162, "sample_rate": 22050, "rms_energy": 0.1741747409105301, "peak_amplitude": 0.925866961479187, "dynamic_range": 1.847402811050415, "zero_crossing_rate": 0.1200386699120603, "spectral_centroid": 2527.8765309090354, "spectral_rolloff": 5273.17551446865, "mfcc_mean": [-100.77223205566406, 80.08590698242188, -1.879504919052124, 27.947925567626953, 1.0925663709640503, 7.767534255981445, 2.157893180847168, 0.2986120879650116, 2.4752771854400635, 6.68317174911499, -6.031764984130859, 5.14708137512207, -4.24323844909668]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Summer Time Cinderella.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Summer Time Cinderella.json"}