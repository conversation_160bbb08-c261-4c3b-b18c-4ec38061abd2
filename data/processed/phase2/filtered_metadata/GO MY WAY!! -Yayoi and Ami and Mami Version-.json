{"file_path": "data\\raw\\ese\\07 Game Music\\GO MY WAY!!\\GO MY WAY!! -<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> Version-.ogg", "filename": "GO MY WAY!! -<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>-", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.261, "original_sr": 48000, "original_channels": 2, "original_duration": 121.67, "file_size_mb": 2.51, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 121.67, "sample_count": 2682815, "sample_rate": 22050, "rms_energy": 0.18675559759140015, "peak_amplitude": 0.8904939889907837, "dynamic_range": 1.7608816623687744, "zero_crossing_rate": 0.16246347208969467, "spectral_centroid": 3017.4139794940984, "spectral_rolloff": 5892.727465447579, "mfcc_mean": [-46.36679458618164, 54.18817901611328, -2.374077796936035, 34.838436126708984, -2.495082139968872, 7.933469772338867, 1.9006060361862183, 1.7191896438598633, 6.5838518142700195, 1.5984853506088257, -0.940637469291687, 6.427657604217529, -3.0884687900543213]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\GO MY WAY!! -<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> Version-.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\GO MY WAY!! -<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> Version-.json"}