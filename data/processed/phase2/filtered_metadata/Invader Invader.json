{"file_path": "data\\raw\\ese\\01 Pop\\Invader Invader\\Invader Invader.ogg", "filename": "Invader Invader", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.241, "original_sr": 44100, "original_channels": 2, "original_duration": 116.47, "file_size_mb": 2.28, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 116.47, "sample_count": 2568111, "sample_rate": 22050, "rms_energy": 0.16637615859508514, "peak_amplitude": 0.8900001645088196, "dynamic_range": 1.718639612197876, "zero_crossing_rate": 0.14379074851101475, "spectral_centroid": 3175.5101680610496, "spectral_rolloff": 7002.777099609375, "mfcc_mean": [-77.01686096191406, 59.46936798095703, 3.4278037548065186, 6.242043972015381, 9.537221908569336, 7.75560998916626, 2.8046133518218994, 1.9200611114501953, 1.087226390838623, 3.95674467086792, -6.320394039154053, 3.2482852935791016, 0.5015565156936646]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Invader Invader.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Invader Invader.json"}