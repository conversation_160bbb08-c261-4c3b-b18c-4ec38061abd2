{"file_path": "data\\raw\\ese\\03 Vocaloid\\Matryoshka\\Matryoshka.ogg", "filename": "<PERSON><PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.261, "original_sr": 44100, "original_channels": 2, "original_duration": 134.84, "file_size_mb": 2.68, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 134.84, "sample_count": 2973234, "sample_rate": 22050, "rms_energy": 0.18231962621212006, "peak_amplitude": 0.8400014638900757, "dynamic_range": 1.6796696186065674, "zero_crossing_rate": 0.14244403261126895, "spectral_centroid": 2851.415600163843, "spectral_rolloff": 6319.924674546423, "mfcc_mean": [-21.206298828125, 72.72955322265625, -9.538954734802246, 17.241281509399414, 17.332002639770508, 11.719021797180176, 4.4242658615112305, 15.228717803955078, -3.7397892475128174, 3.7828147411346436, -2.9746313095092773, 7.422318935394287, 0.5141385793685913]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Matryoshka.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Matryoshka.json"}