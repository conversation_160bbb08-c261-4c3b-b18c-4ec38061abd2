{"file_path": "data\\raw\\ese\\01 Pop\\<PERSON>i ga <PERSON><PERSON>\\<PERSON>i ga <PERSON><PERSON>.ogg", "filename": "<PERSON><PERSON> ga <PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.242, "original_sr": 44100, "original_channels": 2, "original_duration": 106.47, "file_size_mb": 2.03, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 106.47, "sample_count": 2347592, "sample_rate": 22050, "rms_energy": 0.13935299217700958, "peak_amplitude": 0.8777636289596558, "dynamic_range": 1.7450788021087646, "zero_crossing_rate": 0.08123473189462495, "spectral_centroid": 1988.6470918333498, "spectral_rolloff": 4174.70867092101, "mfcc_mean": [-136.01902770996094, 103.86335754394531, -4.955436706542969, 32.28618240356445, 10.994869232177734, -2.133530378341675, 5.839565277099609, -3.266664743423462, 4.885802268981934, -3.764090061187744, -7.3465046882629395, 1.3348585367202759, -2.7009634971618652]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Kimi ga Kureta Natsu.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Kimi ga Kureta Natsu.json"}