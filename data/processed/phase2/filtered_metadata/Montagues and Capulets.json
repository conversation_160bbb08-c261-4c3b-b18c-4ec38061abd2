{"file_path": "data\\raw\\ese\\06 Classical\\Montagues and Capulets\\Montagues and Capulets.ogg", "filename": "Montagues and Capulets", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.292, "original_sr": 48000, "original_channels": 2, "original_duration": 116.07, "file_size_mb": 3.52, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 116.07, "sample_count": 2559430, "sample_rate": 22050, "rms_energy": 0.15388578176498413, "peak_amplitude": 0.8616641759872437, "dynamic_range": 1.6775671243667603, "zero_crossing_rate": 0.08618198249024805, "spectral_centroid": 1624.045889279425, "spectral_rolloff": 3166.037753449127, "mfcc_mean": [-123.6962890625, 124.56206512451172, -23.242874145507812, 23.100736618041992, -9.09079647064209, 0.4539240002632141, -9.880298614501953, -3.055971145629883, -8.523079872131348, -2.7573530673980713, -4.846098899841309, 2.524481773376465, 2.0483803749084473]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Montagues and Capulets.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Montagues and Capulets.json"}