{"file_path": "data\\raw\\ese\\09 Namco Original\\Monochrome Utopia\\Monochrome Utopia.ogg", "filename": "Monochrome Utopia", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.394, "original_sr": 48000, "original_channels": 2, "original_duration": 118.44, "file_size_mb": 6.44, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 118.44, "sample_count": 2611602, "sample_rate": 22050, "rms_energy": 0.1712946742773056, "peak_amplitude": 0.8587051033973694, "dynamic_range": 1.7057316303253174, "zero_crossing_rate": 0.11498894211919232, "spectral_centroid": 2608.73758526164, "spectral_rolloff": 5464.36584029939, "mfcc_mean": [-84.15168762207031, 77.61111450195312, -9.067704200744629, 18.797985076904297, 4.076701641082764, 15.114561080932617, 4.740843772888184, -0.43454039096832275, 5.707607746124268, 1.7306485176086426, -3.2882468700408936, 1.8677157163619995, -0.9580198526382446]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Monochrome Utopia.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Monochrome Utopia.json"}