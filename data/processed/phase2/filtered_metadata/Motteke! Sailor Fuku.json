{"file_path": "data\\raw\\ese\\02 Anime\\<PERSON><PERSON><PERSON>! Sailor Fuku\\<PERSON>tteke! Sailor Fuku.ogg", "filename": "Motteke! Sailor Fuku", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.153, "original_sr": 44100, "original_channels": 2, "original_duration": 85.57, "file_size_mb": 1.51, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 85.57, "sample_count": 1886862, "sample_rate": 22050, "rms_energy": 0.23056969046592712, "peak_amplitude": 0.9743699431419373, "dynamic_range": 1.9363770484924316, "zero_crossing_rate": 0.19331420302326371, "spectral_centroid": 3544.6315349213814, "spectral_rolloff": 7575.493023645212, "mfcc_mean": [-1.4472014904022217, 50.29785919189453, 2.0823657512664795, 11.645456314086914, 17.63788604736328, 7.357828617095947, 9.47031307220459, 9.19821834564209, 2.3830645084381104, 8.448860168457031, -3.6514265537261963, 6.542398452758789, 0.033726006746292114]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Motteke! Sailor Fuku.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Motteke! Sailor Fuku.json"}