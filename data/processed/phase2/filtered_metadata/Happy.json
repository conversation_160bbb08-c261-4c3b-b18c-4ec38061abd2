{"file_path": "data\\raw\\ese\\02 Anime\\Happy☆彡\\Happy.ogg", "filename": "Happy", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.22, "original_sr": 44100, "original_channels": 2, "original_duration": 90.37, "file_size_mb": 2.87, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 90.37, "sample_count": 1992704, "sample_rate": 22050, "rms_energy": 0.2582540810108185, "peak_amplitude": 0.9640775322914124, "dynamic_range": 1.9087343215942383, "zero_crossing_rate": 0.1273404387683021, "spectral_centroid": 3047.726852093287, "spectral_rolloff": 6372.030464836726, "mfcc_mean": [-29.389911651611328, 52.44291305541992, 6.333062171936035, 34.81578063964844, 0.952213704586029, 14.032000541687012, 3.7354867458343506, 9.176753997802734, 6.61838960647583, 4.421231746673584, -1.0796823501586914, 7.158865451812744, -4.0188984870910645]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Happy.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Happy.json"}