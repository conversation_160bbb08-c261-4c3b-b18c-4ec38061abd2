{"file_path": "data\\raw\\ese\\01 Pop\\Ukiyo CROSSING\\Ukiyo CROSSING.ogg", "filename": "Ukiyo CROSSING", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.251, "original_sr": 44100, "original_channels": 2, "original_duration": 98.17, "file_size_mb": 3.09, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 98.17, "sample_count": 2164736, "sample_rate": 22050, "rms_energy": 0.22463630139827728, "peak_amplitude": 0.9376038312911987, "dynamic_range": 1.8422844409942627, "zero_crossing_rate": 0.14169912638182786, "spectral_centroid": 3050.713621326099, "spectral_rolloff": 6306.98303381414, "mfcc_mean": [-40.42323684692383, 51.92924880981445, 0.177939772605896, 31.47139549255371, 16.5920352935791, 12.506424903869629, 9.558568000793457, 16.020244598388672, 0.4973556101322174, 7.338236331939697, -2.102043628692627, 2.982527256011963, -2.5450751781463623]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Ukiyo CROSSING.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Ukiyo CROSSING.json"}