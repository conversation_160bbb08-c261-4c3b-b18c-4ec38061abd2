{"file_path": "data\\raw\\ese\\01 Pop\\Dramaturgy\\Dramaturgy.ogg", "filename": "Dramaturgy", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.244, "original_sr": 44100, "original_channels": 2, "original_duration": 93.95, "file_size_mb": 2.94, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 93.95, "sample_count": 2071651, "sample_rate": 22050, "rms_energy": 0.18542635440826416, "peak_amplitude": 0.8691840171813965, "dynamic_range": 1.6919796466827393, "zero_crossing_rate": 0.12207948210093897, "spectral_centroid": 3052.88874965982, "spectral_rolloff": 6984.153698920497, "mfcc_mean": [-94.21756744384766, 67.89202117919922, 16.102781295776367, 22.855792999267578, 23.901628494262695, 1.5678538084030151, 3.6826963424682617, 8.300154685974121, -4.229844093322754, 5.548933982849121, -4.081900119781494, -2.229959487915039, -2.2957751750946045]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Dramaturgy.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Dramaturgy.json"}