{"file_path": "data\\raw\\ese\\04 Children and Folk\\Baby Shark\\Baby Shark -Nonochan Version-.ogg", "filename": "<PERSON> -<PERSON><PERSON><PERSON> Version-", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.238, "original_sr": 44100, "original_channels": 2, "original_duration": 98.25, "file_size_mb": 3.18, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 98.25, "sample_count": 2166389, "sample_rate": 22050, "rms_energy": 0.16998924314975739, "peak_amplitude": 0.9297597408294678, "dynamic_range": 1.8394966125488281, "zero_crossing_rate": 0.13042047569116258, "spectral_centroid": 2798.5316626358494, "spectral_rolloff": 5964.5293554872105, "mfcc_mean": [-100.69042205810547, 68.5889663696289, 2.400895118713379, 21.450515747070312, -1.2535806894302368, 9.862030982971191, -4.350216865539551, 7.711808681488037, -4.473611354827881, 1.6606758832931519, -1.975897192955017, 4.7566633224487305, -3.7058722972869873]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Baby Shark -Nonochan Version-.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Baby Shark -Nonochan Version-.json"}