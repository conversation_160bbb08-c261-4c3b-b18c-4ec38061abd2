{"file_path": "data\\raw\\ese\\06 Classical\\Utsukushiku Isogashiki Danube\\Utsukushiku Isogashiki Danube.ogg", "filename": "Utsukus<PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.286, "original_sr": 44100, "original_channels": 2, "original_duration": 146.23, "file_size_mb": 2.98, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 146.23, "sample_count": 3224311, "sample_rate": 22050, "rms_energy": 0.11491324007511139, "peak_amplitude": 0.6578022837638855, "dynamic_range": 1.3072032928466797, "zero_crossing_rate": 0.07375481303588441, "spectral_centroid": 1494.2564115986197, "spectral_rolloff": 2940.0463851681584, "mfcc_mean": [-163.56932067871094, 141.6363525390625, -16.54827308654785, 15.238729476928711, 11.37218189239502, 1.1816908121109009, -3.322356700897217, -2.705578088760376, -12.413881301879883, -1.3546277284622192, -8.334471702575684, -1.2140790224075317, -4.8286943435668945]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Utsukushiku Isogashiki Danube.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Utsukushiku Isogashiki Danube.json"}