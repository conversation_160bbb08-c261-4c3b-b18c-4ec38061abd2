{"file_path": "data\\raw\\ese\\02 Anime\\Hug Shichao\\Hug Shichao.ogg", "filename": "<PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.166, "original_sr": 44100, "original_channels": 2, "original_duration": 72.17, "file_size_mb": 2.18, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 72.17, "sample_count": 1591296, "sample_rate": 22050, "rms_energy": 0.2735382616519928, "peak_amplitude": 0.946334958076477, "dynamic_range": 1.889350175857544, "zero_crossing_rate": 0.1341612807574783, "spectral_centroid": 2788.782992184872, "spectral_rolloff": 5854.9742022907285, "mfcc_mean": [-29.57094383239746, 62.41815948486328, -3.807068109512329, 20.695232391357422, -0.41927599906921387, 9.2150297164917, 7.5517897605896, 6.1475653648376465, -1.6265196800231934, 9.533577919006348, -3.966639518737793, 7.620054721832275, 2.4363889694213867]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Hug Shichao.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Hug Shichao.json"}