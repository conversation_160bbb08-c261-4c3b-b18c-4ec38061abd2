{"file_path": "data\\raw\\ese\\02 Anime\\Cry Baby\\Cry Baby (cover).ogg", "filename": "Cry Baby (cover)", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.31, "original_sr": 48000, "original_channels": 2, "original_duration": 94.13, "file_size_mb": 5.04, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 94.13, "sample_count": 2075669, "sample_rate": 22050, "rms_energy": 0.20872889459133148, "peak_amplitude": 0.92608642578125, "dynamic_range": 1.8394083976745605, "zero_crossing_rate": 0.12103378352342786, "spectral_centroid": 2661.535438097816, "spectral_rolloff": 5827.9229262195595, "mfcc_mean": [-53.49724197387695, 72.27922058105469, 1.2002569437026978, 21.206449508666992, 8.46621036529541, 5.4508867263793945, 1.6658190488815308, 10.46467113494873, 1.5763144493103027, 10.152435302734375, -2.336289405822754, 5.488271713256836, -1.671858549118042]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Cry Baby (cover).npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Cry Baby (cover).json"}