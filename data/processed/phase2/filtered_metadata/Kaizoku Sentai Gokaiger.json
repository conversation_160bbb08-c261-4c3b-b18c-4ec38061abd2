{"file_path": "data\\raw\\ese\\02 Anime\\Kaizoku Sentai Gokaiger\\Kaizoku Sentai Gokaiger.ogg", "filename": "Kaizoku Sentai Gokaiger", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.152, "original_sr": 44100, "original_channels": 2, "original_duration": 82.72, "file_size_mb": 1.55, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 82.72, "sample_count": 1823896, "sample_rate": 22050, "rms_energy": 0.20565013587474823, "peak_amplitude": 0.8965108394622803, "dynamic_range": 1.7784422636032104, "zero_crossing_rate": 0.17207110734458322, "spectral_centroid": 3152.3675402367894, "spectral_rolloff": 6744.951037573674, "mfcc_mean": [-26.08774185180664, 58.512725830078125, -0.5916018486022949, 20.681856155395508, 11.447466850280762, 6.9242167472839355, 0.16213791072368622, 6.462282657623291, -3.3195605278015137, 5.492685794830322, -6.348502159118652, -0.1143728569149971, -3.137601613998413]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Kaizoku Sentai Gokaiger.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Kaizoku Sentai Gokaiger.json"}