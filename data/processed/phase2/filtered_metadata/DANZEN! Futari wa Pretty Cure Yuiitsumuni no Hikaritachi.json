{"file_path": "data\\raw\\ese\\02 Anime\\DANZEN! <PERSON><PERSON><PERSON> wa Pretty Cure ～Yuiitsumuni no Hikaritachi～\\DANZEN! <PERSON><PERSON><PERSON> wa Pretty Cure Yuiitsu<PERSON>ni no <PERSON><PERSON><PERSON>chi.ogg", "filename": "DANZEN! <PERSON><PERSON><PERSON> wa Pretty Cure Yuiitsumuni no Hikaritachi", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.182, "original_sr": 44100, "original_channels": 2, "original_duration": 88.86, "file_size_mb": 1.75, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 88.86, "sample_count": 1959318, "sample_rate": 22050, "rms_energy": 0.17508751153945923, "peak_amplitude": 0.9765479564666748, "dynamic_range": 1.8014228343963623, "zero_crossing_rate": 0.19644727225960282, "spectral_centroid": 3920.197991992193, "spectral_rolloff": 8113.437482137608, "mfcc_mean": [-49.75934982299805, 42.151893615722656, 17.784473419189453, 15.972589492797852, 8.976763725280762, 2.1912407875061035, 7.118377685546875, 4.365063190460205, 1.5712910890579224, 8.26152515411377, -5.722309112548828, 8.069242477416992, 1.0251599550247192]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\DANZEN! <PERSON><PERSON><PERSON> wa Pretty Cure Yuiitsumuni no Hikaritachi.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\DANZEN! <PERSON><PERSON><PERSON> wa Pretty Cure Yuiitsumuni no Hikaritachi.json"}