{"file_path": "data\\raw\\ese\\01 Pop\\Minimoni. Telephone! Ring Ring Ring\\Minimoni. Telephone! Ring Ring Ring.ogg", "filename": "Minimoni. Telephone! Ring Ring Ring", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.405, "original_sr": 48000, "original_channels": 2, "original_duration": 135.0, "file_size_mb": 4.65, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 135.0, "sample_count": 2976692, "sample_rate": 22050, "rms_energy": 0.2182590365409851, "peak_amplitude": 1.0169055461883545, "dynamic_range": 2.02628231048584, "zero_crossing_rate": 0.20163806853607671, "spectral_centroid": 3628.3742096660653, "spectral_rolloff": 7207.432343568596, "mfcc_mean": [-32.96018600463867, 39.472412109375, 7.791008472442627, 23.24378204345703, 3.2018182277679443, 5.328217029571533, 0.5185663104057312, 7.020434379577637, -2.2406694889068604, 6.087910175323486, -1.034432291984558, 4.346263408660889, -1.658191204071045]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Minimoni. Telephone! Ring Ring Ring.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Minimoni. Telephone! Ring Ring Ring.json"}