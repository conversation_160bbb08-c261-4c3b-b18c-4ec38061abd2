{"file_path": "data\\raw\\ese\\02 Anime\\Nandedarou -Ryousan Version-\\Nandedarou -Ryousan Version-.ogg", "filename": "Nandedarou -Ryousan Version-", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.223, "original_sr": 44100, "original_channels": 2, "original_duration": 89.72, "file_size_mb": 3.15, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 89.72, "sample_count": 1978368, "sample_rate": 22050, "rms_energy": 0.22252199053764343, "peak_amplitude": 0.8900418877601624, "dynamic_range": 1.7667170763015747, "zero_crossing_rate": 0.09175303707147478, "spectral_centroid": 2406.842000544667, "spectral_rolloff": 5331.548666417368, "mfcc_mean": [-106.82427978515625, 74.7908935546875, 10.512862205505371, 21.373092651367188, 3.8611886501312256, 5.500904083251953, 10.09649658203125, 10.484476089477539, -5.122503757476807, 2.578925132751465, 2.125464916229248, -5.421117782592773, -2.8791635036468506]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Nandedarou -Ryousan Version-.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Nandedarou -Ryousan Version-.json"}