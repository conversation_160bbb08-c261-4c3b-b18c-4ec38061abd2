{"file_path": "data\\raw\\ese\\07 Game Music\\Make Inu Henjou\\Make Inu Henjou.ogg", "filename": "<PERSON> Inu He<PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.171, "original_sr": 44100, "original_channels": 2, "original_duration": 92.29, "file_size_mb": 1.62, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 92.29, "sample_count": 2035075, "sample_rate": 22050, "rms_energy": 0.1549946367740631, "peak_amplitude": 0.881116509437561, "dynamic_range": 1.7320139408111572, "zero_crossing_rate": 0.159546481918239, "spectral_centroid": 3165.27526366705, "spectral_rolloff": 6983.098595961085, "mfcc_mean": [-92.59955596923828, 58.13364791870117, 10.34157943725586, 18.472774505615234, 16.381988525390625, 8.27888011932373, 12.655047416687012, 7.365234375, -1.3791940212249756, 5.314786434173584, -5.503260612487793, 2.590015172958374, -1.9871865510940552]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Make Inu Henjou.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Make Inu Henjou.json"}