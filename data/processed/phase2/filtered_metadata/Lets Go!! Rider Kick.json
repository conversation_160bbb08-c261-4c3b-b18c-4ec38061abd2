{"file_path": "data\\raw\\ese\\02 Anime\\Let’s Go!! Rider Kick\\Lets Go!! Rider Kick.ogg", "filename": "Lets Go!! Rider Kick", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.23, "original_sr": 44100, "original_channels": 2, "original_duration": 78.67, "file_size_mb": 2.73, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 78.67, "sample_count": 1734656, "sample_rate": 22050, "rms_energy": 0.19209611415863037, "peak_amplitude": 0.8844846487045288, "dynamic_range": 1.7637606859207153, "zero_crossing_rate": 0.12148924484545588, "spectral_centroid": 2267.9175077260516, "spectral_rolloff": 4410.700829833192, "mfcc_mean": [-70.4079818725586, 86.21857452392578, -35.67795944213867, 10.57041072845459, -9.598273277282715, 1.8595948219299316, 4.965285301208496, 5.652439594268799, -4.258169651031494, 6.767343521118164, -1.5239677429199219, -3.6855809688568115, -6.610617637634277]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Lets Go!! Rider Kick.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Lets Go!! Rider Kick.json"}