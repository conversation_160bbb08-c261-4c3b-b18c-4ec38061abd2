{"file_path": "data\\raw\\ese\\01 Pop\\<PERSON><PERSON> no <PERSON>\\<PERSON>su no Donasama.ogg", "filename": "<PERSON><PERSON> no <PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.256, "original_sr": 44100, "original_channels": 2, "original_duration": 102.73, "file_size_mb": 3.04, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 102.73, "sample_count": 2265088, "sample_rate": 22050, "rms_energy": 0.22127456963062286, "peak_amplitude": 0.9859805107116699, "dynamic_range": 1.8942774534225464, "zero_crossing_rate": 0.15472876942090397, "spectral_centroid": 2971.989351700756, "spectral_rolloff": 6091.884285619703, "mfcc_mean": [-22.259693145751953, 59.9635009765625, 5.183919906616211, 33.85944366455078, -3.433098554611206, 0.7640522122383118, 7.194336891174316, 11.601759910583496, 7.489221572875977, 9.885519027709961, -0.35874852538108826, 3.101738691329956, -5.793919563293457]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Natsu no Donasama.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Natsu no Donasama.json"}