{"file_path": "data\\raw\\ese\\01 Pop\\Funa Funa Funassyi♪\\Funa Funa Funassyi.ogg", "filename": "<PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.233, "original_sr": 44100, "original_channels": 2, "original_duration": 113.65, "file_size_mb": 2.16, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 113.65, "sample_count": 2506017, "sample_rate": 22050, "rms_energy": 0.18458192050457, "peak_amplitude": 0.877342939376831, "dynamic_range": 1.7458264827728271, "zero_crossing_rate": 0.19293892444458632, "spectral_centroid": 3513.61906323151, "spectral_rolloff": 7237.122611960546, "mfcc_mean": [-34.294681549072266, 44.105567932128906, -7.941600322723389, 15.768044471740723, 14.333934783935547, 9.337897300720215, 5.8049635887146, 9.875657081604004, 3.3225371837615967, 9.296162605285645, -5.925341606140137, 0.7294979691505432, -2.6467440128326416]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Funa Funa Funassyi.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Funa Funa Funassyi.json"}