{"file_path": "data\\raw\\ese\\02 Anime\\Miracle Go! Princess Pretty Cure\\Miracle Go! Princess Pretty Cure.ogg", "filename": "Miracle Go! Princess <PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.209, "original_sr": 44100, "original_channels": 2, "original_duration": 106.0, "file_size_mb": 2.12, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 106.0, "sample_count": 2337300, "sample_rate": 22050, "rms_energy": 0.1765630543231964, "peak_amplitude": 0.8462696075439453, "dynamic_range": 1.6163039207458496, "zero_crossing_rate": 0.19057790427890933, "spectral_centroid": 3543.3398293604096, "spectral_rolloff": 7667.02288954706, "mfcc_mean": [-59.627662658691406, 57.161075592041016, 18.278854370117188, 11.926651000976562, 13.641519546508789, 6.747564315795898, 8.50979995727539, 0.5189803838729858, -5.950726509094238, -0.7457373142242432, -8.064308166503906, 0.782734751701355, -5.198084354400635]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Miracle Go! Princess Pretty Cure.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Miracle Go! Princess Pretty Cure.json"}