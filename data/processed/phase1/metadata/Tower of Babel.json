{"file_path": "data\\raw\\ese\\07 Game Music\\Tower of Babel\\Tower of Babel.ogg", "filename": "Tower of Babel", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.208, "original_sr": 44100, "original_channels": 2, "original_duration": 112.65, "file_size_mb": 2.17, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 112.65, "sample_count": 2483950, "sample_rate": 22050, "rms_energy": 0.14960965514183044, "peak_amplitude": 0.7918068170547485, "dynamic_range": 1.5128878355026245, "zero_crossing_rate": 0.0998951785346249, "spectral_centroid": 1867.771737443741, "spectral_rolloff": 3769.963703957453, "mfcc_mean": [-117.8173599243164, 106.65579986572266, -14.452540397644043, 30.92941665649414, -1.4269919395446777, 0.5807162523269653, -5.898568153381348, 1.9332600831985474, -8.933984756469727, -2.9587552547454834, -6.484519958496094, -5.993119716644287, -5.398402690887451]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Tower of Babel.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Tower of Babel.json"}