{"file_path": "data\\raw\\ese\\01 Pop\\Memeshikute\\Memeshikute.ogg", "filename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.243, "original_sr": 44100, "original_channels": 2, "original_duration": 100.44, "file_size_mb": 2.94, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 100.44, "sample_count": 2214679, "sample_rate": 22050, "rms_energy": 0.1920221447944641, "peak_amplitude": 0.843783974647522, "dynamic_range": 1.672710657119751, "zero_crossing_rate": 0.1589487528172677, "spectral_centroid": 3233.208192568802, "spectral_rolloff": 7054.224580931432, "mfcc_mean": [-33.938350677490234, 57.522979736328125, 5.846433639526367, 14.293024063110352, 10.070659637451172, 13.443846702575684, 0.06637131422758102, 11.243980407714844, -6.004722595214844, 9.157962799072266, -0.37323763966560364, 2.810955286026001, -1.056807279586792]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Memeshikute.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Memeshikute.json"}