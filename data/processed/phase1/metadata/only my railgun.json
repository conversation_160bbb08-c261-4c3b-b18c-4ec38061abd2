{"file_path": "data\\raw\\ese\\02 Anime\\only my railgun\\only my railgun.ogg", "filename": "only my railgun", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.222, "original_sr": 44100, "original_channels": 2, "original_duration": 94.25, "file_size_mb": 2.97, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 94.25, "sample_count": 2078224, "sample_rate": 22050, "rms_energy": 0.15041227638721466, "peak_amplitude": 0.8281940221786499, "dynamic_range": 1.585936427116394, "zero_crossing_rate": 0.20081793122690886, "spectral_centroid": 3889.576068789599, "spectral_rolloff": 8050.370967470366, "mfcc_mean": [-69.99239349365234, 40.90309524536133, 20.26849937438965, 17.512727737426758, 10.609871864318848, 7.638806343078613, 1.6121947765350342, 1.8318411111831665, 0.8977102041244507, 6.98962926864624, -8.146093368530273, 3.2262074947357178, -1.8918728828430176]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\only my railgun.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\only my railgun.json"}