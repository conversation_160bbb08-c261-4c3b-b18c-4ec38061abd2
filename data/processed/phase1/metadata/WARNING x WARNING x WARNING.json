{"file_path": "data\\raw\\ese\\05 Variety\\WARNING × WARNING × WARNING\\WARNING x WARNING x WARNING.ogg", "filename": "WARNING x WARNING x WARNING", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.284, "original_sr": 44100, "original_channels": 2, "original_duration": 125.66, "file_size_mb": 3.6, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 125.66, "sample_count": 2770739, "sample_rate": 22050, "rms_energy": 0.20034164190292358, "peak_amplitude": 0.8512312173843384, "dynamic_range": 1.691352367401123, "zero_crossing_rate": 0.14059252009423504, "spectral_centroid": 3194.0111235108975, "spectral_rolloff": 7273.372513121882, "mfcc_mean": [-44.43281555175781, 56.44316864013672, 3.0817346572875977, 14.1331787109375, 15.448049545288086, 6.324985504150391, 17.512083053588867, 8.298269271850586, 0.8136367797851562, 13.095396041870117, 2.181405782699585, 6.318009853363037, 1.6453959941864014]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\WARNING x WARNING x WARNING.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\WARNING x WARNING x WARNING.json"}