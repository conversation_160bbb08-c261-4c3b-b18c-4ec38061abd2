{"file_path": "data\\raw\\ese\\07 Game Music\\EAT ‘EM UP!\\EAT EM UP!.ogg", "filename": "EAT EM UP!", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.211, "original_sr": 44100, "original_channels": 2, "original_duration": 115.51, "file_size_mb": 2.05, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 115.51, "sample_count": 2547055, "sample_rate": 22050, "rms_energy": 0.13600750267505646, "peak_amplitude": 0.7487537860870361, "dynamic_range": 1.4784221649169922, "zero_crossing_rate": 0.16312313520728644, "spectral_centroid": 3248.3439607514765, "spectral_rolloff": 6774.661667713568, "mfcc_mean": [-109.46176147460938, 58.3387565612793, 21.35249137878418, 4.690698146820068, 4.92318868637085, 3.3581855297088623, -4.003080368041992, 10.164868354797363, -1.2025777101516724, 8.830260276794434, -2.652848243713379, 0.9693254232406616, -5.101379871368408]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\EAT EM UP!.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\EAT EM UP!.json"}