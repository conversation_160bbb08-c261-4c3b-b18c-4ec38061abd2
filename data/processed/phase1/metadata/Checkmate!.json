{"file_path": "data\\raw\\ese\\02 Anime\\Checkmate!\\Checkmate!.ogg", "filename": "Checkmate!", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.218, "original_sr": 48000, "original_channels": 2, "original_duration": 91.74, "file_size_mb": 1.79, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 91.74, "sample_count": 2022787, "sample_rate": 22050, "rms_energy": 0.17289870977401733, "peak_amplitude": 0.9081160426139832, "dynamic_range": 1.8114124536514282, "zero_crossing_rate": 0.12748626732156415, "spectral_centroid": 2947.8671810058836, "spectral_rolloff": 6607.071791803103, "mfcc_mean": [-81.08493041992188, 60.47479248046875, 17.335407257080078, 27.159841537475586, 9.963334083557129, 11.3392972946167, 0.7985423803329468, 4.558980464935303, -2.883441686630249, 4.089847564697266, -7.3065948486328125, 0.9933992028236389, -1.5503908395767212]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Checkmate!.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Checkmate!.json"}