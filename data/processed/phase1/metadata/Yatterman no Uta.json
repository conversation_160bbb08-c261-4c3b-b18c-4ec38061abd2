{"file_path": "data\\raw\\ese\\02 Anime\\Ya<PERSON><PERSON> no Uta (Mizuumi Boys _ Girls Chorus)\\Yatterman no Uta.ogg", "filename": "<PERSON><PERSON><PERSON> no <PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.187, "original_sr": 44100, "original_channels": 2, "original_duration": 78.02, "file_size_mb": 2.42, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 78.02, "sample_count": 1720320, "sample_rate": 22050, "rms_energy": 0.255016952753067, "peak_amplitude": 0.9312453269958496, "dynamic_range": 1.8517463207244873, "zero_crossing_rate": 0.12567685282468016, "spectral_centroid": 2449.395556897246, "spectral_rolloff": 4841.7448870662565, "mfcc_mean": [-38.10953140258789, 73.3755111694336, -16.016780853271484, 37.98553466796875, -13.880215644836426, 9.873885154724121, -0.4480613172054291, 3.139543056488037, -7.6128082275390625, 0.5428435802459717, -8.160745620727539, -2.553412914276123, -4.650485515594482]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Yatterman no Uta.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Yatterman no Uta.json"}