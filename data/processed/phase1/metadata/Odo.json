{"file_path": "data\\raw\\ese\\01 Pop\\Odo\\Odo.ogg", "filename": "<PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.243, "original_sr": 48000, "original_channels": 2, "original_duration": 106.44, "file_size_mb": 2.01, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 106.44, "sample_count": 2347050, "sample_rate": 22050, "rms_energy": 0.1866517812013626, "peak_amplitude": 0.9644193649291992, "dynamic_range": 1.9016681909561157, "zero_crossing_rate": 0.13411270191521266, "spectral_centroid": 2840.1434938018797, "spectral_rolloff": 5852.867850966126, "mfcc_mean": [-55.079376220703125, 66.85719299316406, -13.890460014343262, 12.403583526611328, -3.951455593109131, 6.239304065704346, 2.6241817474365234, 0.12764747440814972, 5.038341522216797, 5.710178852081299, -1.3957555294036865, 5.230594158172607, -1.105738639831543]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Odo.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Odo.json"}