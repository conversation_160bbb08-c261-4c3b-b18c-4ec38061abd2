{"file_path": "data\\raw\\ese\\02 Anime\\Itsumo Nando Demo\\Itsumo Nando Demo.ogg", "filename": "<PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.218, "original_sr": 44100, "original_channels": 2, "original_duration": 103.73, "file_size_mb": 2.61, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 103.73, "sample_count": 2287205, "sample_rate": 22050, "rms_energy": 0.1412554234266281, "peak_amplitude": 0.7851576209068298, "dynamic_range": 1.5166699886322021, "zero_crossing_rate": 0.0639470304456692, "spectral_centroid": 1656.5579073894637, "spectral_rolloff": 3102.7548056579426, "mfcc_mean": [-233.28662109375, 112.94808197021484, 31.874265670776367, 5.3902716636657715, 12.821183204650879, -12.83155345916748, -3.268667221069336, -18.534791946411133, -24.75463104248047, -3.5255510807037354, -17.827526092529297, -15.532477378845215, -9.358238220214844]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Itsumo Nando Demo.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Itsumo Nando Demo.json"}