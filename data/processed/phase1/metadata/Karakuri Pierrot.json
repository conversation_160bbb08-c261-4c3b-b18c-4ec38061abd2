{"file_path": "data\\raw\\ese\\03 Vocaloid\\<PERSON><PERSON><PERSON>\\Karakuri Pierrot.ogg", "filename": "<PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.215, "original_sr": 44100, "original_channels": 2, "original_duration": 117.45, "file_size_mb": 1.96, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 117.45, "sample_count": 2589729, "sample_rate": 22050, "rms_energy": 0.1879192292690277, "peak_amplitude": 0.952399492263794, "dynamic_range": 1.82633638381958, "zero_crossing_rate": 0.10896325700484286, "spectral_centroid": 2148.9763536563073, "spectral_rolloff": 4423.992112795575, "mfcc_mean": [-60.41865921020508, 100.72551727294922, -18.71575164794922, 14.776567459106445, 2.6587932109832764, 6.871374607086182, -3.371040105819702, 0.40186724066734314, -4.490045547485352, -0.716880202293396, -7.881336212158203, -0.9469860196113586, -4.121837139129639]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Karakuri Pierrot.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Karakuri Pierrot.json"}