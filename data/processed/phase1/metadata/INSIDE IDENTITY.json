{"file_path": "data\\raw\\ese\\02 Anime\\INSIDE IDENTITY\\INSIDE IDENTITY.ogg", "filename": "INSIDE IDENTITY", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.211, "original_sr": 44100, "original_channels": 2, "original_duration": 92.08, "file_size_mb": 2.78, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 92.08, "sample_count": 2030346, "sample_rate": 22050, "rms_energy": 0.200112983584404, "peak_amplitude": 0.8850412368774414, "dynamic_range": 1.7633466720581055, "zero_crossing_rate": 0.22186571699287697, "spectral_centroid": 3793.6836605775957, "spectral_rolloff": 7825.695930053896, "mfcc_mean": [-11.519734382629395, 40.031558990478516, 3.2479441165924072, 17.5081729888916, 11.122191429138184, 8.756706237792969, 12.557194709777832, 9.156135559082031, 5.778810024261475, 4.704009532928467, -6.213295936584473, 6.752684116363525, -0.5680398941040039]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\INSIDE IDENTITY.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\INSIDE IDENTITY.json"}