{"file_path": "data\\raw\\ese\\04 Children and Folk\\Momotarou\\Momotarou.ogg", "filename": "<PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.156, "original_sr": 44100, "original_channels": 2, "original_duration": 82.18, "file_size_mb": 1.58, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 82.18, "sample_count": 1811992, "sample_rate": 22050, "rms_energy": 0.2814084589481354, "peak_amplitude": 1.0534534454345703, "dynamic_range": 2.082946538925171, "zero_crossing_rate": 0.09754300737111582, "spectral_centroid": 2282.767601907289, "spectral_rolloff": 5136.094743114407, "mfcc_mean": [-58.658607482910156, 93.9361343383789, 18.521509170532227, 20.866119384765625, -0.9834537506103516, 4.728836536407471, 0.9306472539901733, 9.742515563964844, -1.2161496877670288, 3.4572951793670654, 2.3305933475494385, 1.7729787826538086, -7.969704627990723]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Momotarou.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Momotarou.json"}