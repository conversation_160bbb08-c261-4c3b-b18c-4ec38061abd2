{"file_path": "data\\raw\\ese\\04 Children and Folk\\Bakuage Sentai Boomboomger\\Bakuage Sentai Boomboomger.ogg", "filename": "Bakuage Sentai Boomboomger", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.234, "original_sr": 48000, "original_channels": 2, "original_duration": 76.4, "file_size_mb": 4.04, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 76.4, "sample_count": 1684560, "sample_rate": 22050, "rms_energy": 0.18138526380062103, "peak_amplitude": 0.9533690214157104, "dynamic_range": 1.8662071228027344, "zero_crossing_rate": 0.17712057386622607, "spectral_centroid": 3567.8407693794256, "spectral_rolloff": 8018.743034091556, "mfcc_mean": [-43.76063537597656, 56.048797607421875, 19.31142234802246, 7.912603855133057, 18.651840209960938, 9.115262031555176, 5.294699192047119, 5.87973690032959, 2.209519624710083, 2.3206028938293457, -3.754267692565918, 5.051911354064941, 2.854055643081665]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Bakuage Sentai Boomboomger.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Bakuage Sentai Boomboomger.json"}