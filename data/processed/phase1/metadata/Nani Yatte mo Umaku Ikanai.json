{"file_path": "data\\raw\\ese\\01 Pop\\Nani Yatte mo Umaku Ikanai\\Nani Yatte mo Umaku Ikanai.ogg", "filename": "<PERSON><PERSON> mo <PERSON>aku <PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.498, "original_sr": 44100, "original_channels": 2, "original_duration": 132.23, "file_size_mb": 8.51, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 132.23, "sample_count": 2915575, "sample_rate": 22050, "rms_energy": 0.16335883736610413, "peak_amplitude": 0.8507227897644043, "dynamic_range": 1.696293830871582, "zero_crossing_rate": 0.11816260494402984, "spectral_centroid": 2862.865905826304, "spectral_rolloff": 6346.107198097289, "mfcc_mean": [-104.83377838134766, 80.8674545288086, 10.77719783782959, 4.13350248336792, 13.920717239379883, 6.798701763153076, 8.595078468322754, 5.706617832183838, -0.7691488265991211, 10.202651977539062, 0.2286464124917984, 6.37016487121582, 2.5693042278289795]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Nani Yatte mo Umaku Ikanai.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Nani Yatte mo Umaku Ikanai.json"}