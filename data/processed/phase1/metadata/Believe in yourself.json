{"file_path": "data\\raw\\ese\\02 Anime\\Believe in yourself\\Believe in yourself.ogg", "filename": "Believe in yourself", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.181, "original_sr": 44100, "original_channels": 2, "original_duration": 87.59, "file_size_mb": 1.66, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 87.59, "sample_count": 1931448, "sample_rate": 22050, "rms_energy": 0.18217074871063232, "peak_amplitude": 0.9082639813423157, "dynamic_range": 1.8030115365982056, "zero_crossing_rate": 0.1383441972568248, "spectral_centroid": 2862.3079800815012, "spectral_rolloff": 5878.844105113636, "mfcc_mean": [-57.278263092041016, 61.36594772338867, -0.6807410717010498, 38.67892837524414, 4.231124401092529, 7.937075614929199, 4.810853958129883, 2.7712624073028564, 0.807732105255127, 1.0671210289001465, -5.744908332824707, 1.440032958984375, -4.506481647491455]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Believe in yourself.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Believe in yourself.json"}