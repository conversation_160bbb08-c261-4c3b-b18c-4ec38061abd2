{"file_path": "data\\raw\\ese\\01 Pop\\Itadaki Babel\\Itadaki Babel.ogg", "filename": "Itadaki <PERSON>l", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.294, "original_sr": 48000, "original_channels": 2, "original_duration": 109.61, "file_size_mb": 3.58, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 109.61, "sample_count": 2416864, "sample_rate": 22050, "rms_energy": 0.20522598922252655, "peak_amplitude": 0.8641796112060547, "dynamic_range": 1.7007851600646973, "zero_crossing_rate": 0.11684081100402458, "spectral_centroid": 2773.909254817488, "spectral_rolloff": 6194.219043734113, "mfcc_mean": [-61.505043029785156, 77.34123992919922, 2.3398303985595703, 8.944265365600586, 6.501709938049316, 5.583620071411133, 3.245443820953369, 8.676844596862793, 6.281245708465576, 0.6739422082901001, -2.6462507247924805, 6.2469964027404785, -2.335725784301758]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Itadaki Babel.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Itadaki Babel.json"}