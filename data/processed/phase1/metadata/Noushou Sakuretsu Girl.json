{"file_path": "data\\raw\\ese\\03 Vocaloid\\Noushou Sakuretsu Girl\\Noushou Sakuretsu Girl.ogg", "filename": "<PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.205, "original_sr": 44100, "original_channels": 2, "original_duration": 105.67, "file_size_mb": 2.07, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 105.67, "sample_count": 2330025, "sample_rate": 22050, "rms_energy": 0.18219488859176636, "peak_amplitude": 0.8765813708305359, "dynamic_range": 1.6788525581359863, "zero_crossing_rate": 0.1571259235607559, "spectral_centroid": 3462.7081325153945, "spectral_rolloff": 8052.968473189271, "mfcc_mean": [-72.45491790771484, 60.51521682739258, 22.44049644470215, 1.2688679695129395, 23.75554084777832, 8.086642265319824, 12.331262588500977, 3.2632367610931396, -6.445573329925537, 8.977324485778809, 1.1705960035324097, 2.357051134109497, 0.7553321123123169]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\<PERSON>ush<PERSON>retsu Girl.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Noushou Sakuretsu Girl.json"}