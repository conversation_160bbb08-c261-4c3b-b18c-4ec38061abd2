{"file_path": "data\\raw\\ese\\02 Anime\\<PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON>.ogg", "filename": "<PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.249, "original_sr": 44100, "original_channels": 2, "original_duration": 125.48, "file_size_mb": 2.73, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 125.48, "sample_count": 2766848, "sample_rate": 22050, "rms_energy": 0.22787395119667053, "peak_amplitude": 0.9339559078216553, "dynamic_range": 1.8591822385787964, "zero_crossing_rate": 0.11868956333834413, "spectral_centroid": 2735.4884110072708, "spectral_rolloff": 6251.815936488206, "mfcc_mean": [-55.049861907958984, 77.7535171508789, 12.921365737915039, 8.583829879760742, -0.32299917936325073, 10.724018096923828, -8.199482917785645, 7.400750160217285, -2.5402374267578125, 6.34157657623291, -1.3966162204742432, 4.770195960998535, -2.9998326301574707]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\<PERSON><PERSON><PERSON>.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\<PERSON><PERSON><PERSON>.json"}