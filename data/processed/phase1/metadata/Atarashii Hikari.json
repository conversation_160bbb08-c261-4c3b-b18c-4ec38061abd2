{"file_path": "data\\raw\\ese\\02 Anime\\Atarashii Hikari\\Atarashii Hikari.ogg", "filename": "<PERSON><PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.097, "original_sr": 44100, "original_channels": 2, "original_duration": 50.6, "file_size_mb": 0.82, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 50.6, "sample_count": 1115648, "sample_rate": 22050, "rms_energy": 0.2292320728302002, "peak_amplitude": 0.8911643028259277, "dynamic_range": 1.7348966598510742, "zero_crossing_rate": 0.14802694058199542, "spectral_centroid": 2987.1027314639873, "spectral_rolloff": 6343.093922466313, "mfcc_mean": [-26.79410171508789, 56.88907241821289, -0.2767289876937866, 22.45527458190918, 4.397882461547852, 4.468836784362793, 3.3151166439056396, 7.871918201446533, -2.88114857673645, 6.242198467254639, -4.667849063873291, 0.011612050235271454, -1.7054544687271118]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Atarashii Hikari.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Atarashii Hikari.json"}