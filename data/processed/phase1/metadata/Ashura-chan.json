{"file_path": "data\\raw\\ese\\01 Pop\\Ashura-chan\\Ashura-chan.ogg", "filename": "<PERSON><PERSON><PERSON><PERSON>an", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.281, "original_sr": 44100, "original_channels": 2, "original_duration": 93.28, "file_size_mb": 4.28, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 93.28, "sample_count": 2056766, "sample_rate": 22050, "rms_energy": 0.25501930713653564, "peak_amplitude": 0.9996968507766724, "dynamic_range": 1.988649845123291, "zero_crossing_rate": 0.1270536248988925, "spectral_centroid": 2667.8820418097985, "spectral_rolloff": 5455.842701981707, "mfcc_mean": [-22.291025161743164, 69.42640686035156, -6.946316719055176, 37.97389602661133, 1.7757346630096436, 11.023523330688477, 2.696136951446533, 6.0595502853393555, 2.387277841567993, 3.9069342613220215, -5.016120433807373, 3.0528883934020996, -2.9371228218078613]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Ashura-chan.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Ashura-chan.json"}