{"file_path": "data\\raw\\ese\\08 Live Festival Mode\\18 Secret Night 2008\\shiny smile -<PERSON><PERSON><PERSON> and Takan<PERSON> Version-.ogg", "filename": "shiny smile -<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> Version-", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.238, "original_sr": 44100, "original_channels": 2, "original_duration": 126.65, "file_size_mb": 2.54, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 126.65, "sample_count": 2792536, "sample_rate": 22050, "rms_energy": 0.13972188532352448, "peak_amplitude": 0.7591731548309326, "dynamic_range": 1.492813229560852, "zero_crossing_rate": 0.17271747536663612, "spectral_centroid": 3414.1022886154774, "spectral_rolloff": 7334.076745639035, "mfcc_mean": [-77.47139739990234, 54.500240325927734, 13.680922508239746, 14.797966003417969, 8.9385347366333, 4.744742393493652, 3.932333469390869, 1.9486922025680542, -4.496917247772217, 4.854074954986572, -10.224886894226074, 3.591829776763916, -2.6542012691497803]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\shiny smile -<PERSON><PERSON><PERSON> and Takane Version-.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\shiny smile -Hibi<PERSON> and Takane Version-.json"}