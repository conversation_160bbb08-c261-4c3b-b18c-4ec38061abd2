{"file_path": "data\\raw\\ese\\06 Classical\\Chikochiko\\Chikochiko.ogg", "filename": "<PERSON><PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.265, "original_sr": 44100, "original_channels": 2, "original_duration": 136.24, "file_size_mb": 2.61, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 136.24, "sample_count": 3004167, "sample_rate": 22050, "rms_energy": 0.1663774698972702, "peak_amplitude": 0.8049930930137634, "dynamic_range": 1.591847538948059, "zero_crossing_rate": 0.1151972629622529, "spectral_centroid": 2778.7844356340265, "spectral_rolloff": 6480.8097605324965, "mfcc_mean": [-79.99201965332031, 69.45043182373047, 8.365577697753906, 24.96503257751465, 20.020370483398438, 7.777230739593506, 5.01640510559082, 10.764848709106445, -2.6743814945220947, 1.6244021654129028, -5.747069835662842, -1.0802429914474487, 2.5387260913848877]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Chikochiko.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Chikochiko.json"}