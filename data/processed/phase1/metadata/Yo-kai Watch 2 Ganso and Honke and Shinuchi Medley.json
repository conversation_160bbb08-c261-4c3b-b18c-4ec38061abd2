{"file_path": "data\\raw\\ese\\07 Game Music\\Yo-kai Watch 2 Gans<PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\\Yo-kai Watch 2 Gans<PERSON> and <PERSON><PERSON> and <PERSON><PERSON>dley.ogg", "filename": "Yo-kai Watch 2 <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.283, "original_sr": 44100, "original_channels": 2, "original_duration": 148.83, "file_size_mb": 2.92, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 148.83, "sample_count": 3281605, "sample_rate": 22050, "rms_energy": 0.13987775146961212, "peak_amplitude": 0.8726992607116699, "dynamic_range": 1.705786943435669, "zero_crossing_rate": 0.11363660601111544, "spectral_centroid": 2317.1012954064436, "spectral_rolloff": 4918.956235983815, "mfcc_mean": [-107.1315689086914, 94.85263061523438, -8.024730682373047, 13.281267166137695, 9.629335403442383, 6.0995941162109375, 2.786029577255249, 4.302816390991211, -1.4352878332138062, 1.1475306749343872, -7.810518264770508, 1.9149948358535767, -2.907510280609131]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Yo-kai Watch 2 Gans<PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Yo-kai Watch 2 Gans<PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.json"}