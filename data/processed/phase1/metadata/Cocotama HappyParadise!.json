{"file_path": "data\\raw\\ese\\02 Anime\\Cocotama Happy～Paradise!\\Cocotama HappyParadise!.ogg", "filename": "Cocotama HappyParadise!", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.231, "original_sr": 44100, "original_channels": 2, "original_duration": 91.76, "file_size_mb": 2.83, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 91.76, "sample_count": 2023317, "sample_rate": 22050, "rms_energy": 0.1765100657939911, "peak_amplitude": 0.828131914138794, "dynamic_range": 1.6284196376800537, "zero_crossing_rate": 0.17147407068414727, "spectral_centroid": 3412.339534066955, "spectral_rolloff": 7470.073579487048, "mfcc_mean": [-51.96595764160156, 54.22018051147461, 10.203890800476074, 10.292645454406738, 14.241443634033203, 7.842979431152344, 0.847197413444519, 4.37883996963501, 0.20577214658260345, 5.697220802307129, -8.654980659484863, 1.0581185817718506, -2.9039764404296875]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Cocotama HappyParadise!.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Cocotama HappyParadise!.json"}