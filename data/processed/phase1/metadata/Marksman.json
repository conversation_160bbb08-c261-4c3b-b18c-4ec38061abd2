{"file_path": "data\\raw\\ese\\01 Pop\\Marksman\\Marksman.ogg", "filename": "Marksman", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.231, "original_sr": 44100, "original_channels": 2, "original_duration": 111.91, "file_size_mb": 1.79, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 111.91, "sample_count": 2467670, "sample_rate": 22050, "rms_energy": 0.3242226541042328, "peak_amplitude": 1.0921798944473267, "dynamic_range": 2.1749792098999023, "zero_crossing_rate": 0.11127747341804979, "spectral_centroid": 2802.013451231318, "spectral_rolloff": 5984.78524235769, "mfcc_mean": [-55.859375, 63.764808654785156, 5.47737455368042, 27.259178161621094, 5.633042812347412, 15.575909614562988, 7.132984161376953, 7.565868854522705, 0.008119802922010422, 8.071703910827637, 1.4106340408325195, 6.607287406921387, 0.9757830500602722]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Marksman.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Marksman.json"}