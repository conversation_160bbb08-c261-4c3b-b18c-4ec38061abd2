{"file_path": "data\\raw\\ese\\06 Classical\\Sonata, Gekkou\\Sonata, Gekkou.ogg", "filename": "Sonata, <PERSON><PERSON><PERSON><PERSON>", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.36, "original_sr": 48000, "original_channels": 2, "original_duration": 142.94, "file_size_mb": 4.29, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 142.94, "sample_count": 3151871, "sample_rate": 22050, "rms_energy": 0.17595000565052032, "peak_amplitude": 0.6970710754394531, "dynamic_range": 1.3919559717178345, "zero_crossing_rate": 0.1289310765157976, "spectral_centroid": 2742.721628005616, "spectral_rolloff": 5651.66304738898, "mfcc_mean": [-68.60118865966797, 68.71156311035156, 2.894916534423828, 45.39965057373047, -2.7172493934631348, 13.453378677368164, 3.3010990619659424, 6.251191139221191, -0.8604955077171326, 5.751822471618652, -1.4021984338760376, 5.695577621459961, 1.8875274658203125]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\Sonata, Gekkou.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\Sonata, Gekkou.json"}