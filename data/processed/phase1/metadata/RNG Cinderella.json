{"file_path": "data\\raw\\ese\\01 Pop\\RNG Cinderella\\RNG Cinderella.ogg", "filename": "RNG Cinderella", "file_extension": ".ogg", "load_success": true, "error_message": null, "processing_time": 0.317, "original_sr": 48000, "original_channels": 2, "original_duration": 114.82, "file_size_mb": 3.38, "standardized_sr": 22050, "standardized_channels": 1, "conversion_applied": true, "audio_format": ".ogg", "bit_depth": null, "encoding": null, "validation_passed": true, "validation_message": "Audio validation passed", "audio_statistics": {"duration_seconds": 114.82, "sample_count": 2531755, "sample_rate": 22050, "rms_energy": 0.20816615223884583, "peak_amplitude": 0.9091463088989258, "dynamic_range": 1.8093198537826538, "zero_crossing_rate": 0.11188977186552072, "spectral_centroid": 2537.447011838639, "spectral_rolloff": 5466.881832975228, "mfcc_mean": [-39.812767028808594, 79.4126968383789, -5.202469348907471, 23.22042465209961, 0.297294944524765, 12.275513648986816, 3.8589131832122803, 7.8553619384765625, 2.8568689823150635, 5.198566913604736, 2.1761889457702637, 6.467402935028076, -0.5377191305160522]}, "save_success": true, "output_audio_path": "data\\processed\\phase1\\audio\\RNG Cinderella.npy", "output_metadata_path": "data\\processed\\phase1\\metadata\\RNG Cinderella.json"}