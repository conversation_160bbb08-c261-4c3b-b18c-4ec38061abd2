{"phase": "Phase 1: Audio Loading & Format Standardization", "timestamp": "2025-07-26 20:14:02", "configuration": {"paths": {"input_root": "data\\raw\\ese", "output_root": "data\\processed\\phase1", "audio_output": "data\\processed\\phase1\\audio", "metadata_output": "data\\processed\\phase1\\metadata", "logs_output": "data\\processed\\phase1\\processing_logs"}, "audio": {"target_sample_rate": 22050, "target_channels": 1, "target_dtype": "float32", "supported_formats": [".ogg", ".wav", ".mp3", ".flac"]}, "memory": {"max_ram_usage_gb": 8.0, "max_files_in_memory": 50, "chunk_size": 1024, "gc_frequency": 10}, "processing": {"max_workers": 4, "batch_size": 25, "validation_subset_size": 100, "timeout_seconds": 30, "retry_attempts": 3}, "quality": {"min_success_rate": 0.95, "min_duration_seconds": 10, "max_duration_seconds": 600, "min_sample_rate": 8000, "max_sample_rate": 192000}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s", "file_encoding": "utf-8", "max_log_size_mb": 100, "backup_count": 5}, "error_handling": {"continue_on_error": true, "save_error_details": true, "create_error_report": true}, "output": {"save_numpy_arrays": true, "save_metadata_json": true, "compress_arrays": false, "metadata_indent": 2}, "validation": {"run_validation": true, "validate_file_integrity": true, "validate_audio_properties": true, "generate_statistics": true}}, "statistics": {"total_files": 2878, "processed_files": 2878, "successful_files": 2878, "failed_files": 0, "skipped_files": 0, "total_processing_time": 0.0, "average_processing_time": 0.0, "total_audio_duration": 331674.5800000004, "errors": []}, "quality_metrics": {"success_rate": 1.0, "meets_quality_gate": true, "total_audio_hours": 92.13, "average_file_duration": 115.24, "processing_speed_files_per_second": 2878.0}}