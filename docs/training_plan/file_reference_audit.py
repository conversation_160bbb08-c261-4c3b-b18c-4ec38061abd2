#!/usr/bin/env python3
"""
Comprehensive audit script for file path references and naming conventions
across the TJA chart generation training pipeline documentation.
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple, Set
import json

class DocumentationAuditor:
    """Comprehensive auditor for documentation file references."""
    
    def __init__(self):
        self.docs_dir = Path(".")
        self.audit_results = {
            "files_audited": 0,
            "total_references": 0,
            "broken_references": [],
            "inconsistent_naming": [],
            "missing_files": [],
            "correct_references": 0,
            "phase_dependency_issues": [],
            "summary": {}
        }
        
        # Get actual filenames
        self.actual_files = self._get_actual_filenames()
        
        # Define expected naming patterns
        self.expected_patterns = {
            "phase_files": r"phase_\d{2}_[a-z_]+\.md",
            "phase_6_5": r"phase_06_5_feature_extraction\.md",
            "readme": r"README\.md"
        }
    
    def _get_actual_filenames(self) -> Set[str]:
        """Get all actual filenames in the documentation directory."""
        actual_files = set()
        
        for file_path in self.docs_dir.glob("*.md"):
            actual_files.add(file_path.name)
        
        return actual_files
    
    def audit_all_documentation(self) -> Dict:
        """Perform comprehensive audit of all documentation files."""
        
        print("🔍 Starting comprehensive documentation audit...")
        
        # Audit each markdown file
        for file_path in self.docs_dir.glob("*.md"):
            print(f"📄 Auditing {file_path.name}...")
            self._audit_single_file(file_path)
        
        # Generate summary
        self._generate_audit_summary()
        
        # Save audit results
        self._save_audit_results()
        
        return self.audit_results
    
    def _audit_single_file(self, file_path: Path):
        """Audit a single documentation file for reference issues."""
        
        self.audit_results["files_audited"] += 1
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all markdown links
            markdown_links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
            
            for link_text, link_target in markdown_links:
                self.audit_results["total_references"] += 1
                
                # Check if it's a phase file reference
                if link_target.endswith('.md') and 'phase_' in link_target:
                    self._validate_phase_reference(
                        file_path.name, link_text, link_target
                    )
                
                # Check for other markdown file references
                elif link_target.endswith('.md'):
                    self._validate_general_reference(
                        file_path.name, link_text, link_target
                    )
            
            # Check phase dependency format
            if file_path.name.startswith('phase_'):
                self._check_phase_dependencies(file_path, content)
        
        except Exception as e:
            print(f"❌ Error auditing {file_path.name}: {str(e)}")
    
    def _validate_phase_reference(self, source_file: str, link_text: str, link_target: str):
        """Validate a phase file reference."""
        
        # Check if target file exists
        if link_target not in self.actual_files:
            self.audit_results["broken_references"].append({
                "source_file": source_file,
                "link_text": link_text,
                "broken_target": link_target,
                "issue": "file_not_found"
            })
            return
        
        # Check naming convention
        if not self._matches_naming_convention(link_target):
            self.audit_results["inconsistent_naming"].append({
                "source_file": source_file,
                "link_text": link_text,
                "target": link_target,
                "issue": "naming_convention_violation"
            })
        
        # Check if link text matches expected format (skip for README.md and audit report as they use descriptive names)
        skip_files = ["README.md", "file_reference_audit_report.md"]
        if source_file not in skip_files and not self._validate_link_text_format(link_text, link_target):
            self.audit_results["inconsistent_naming"].append({
                "source_file": source_file,
                "link_text": link_text,
                "target": link_target,
                "issue": "link_text_format_mismatch"
            })
        
        self.audit_results["correct_references"] += 1
    
    def _validate_general_reference(self, source_file: str, link_text: str, link_target: str):
        """Validate a general markdown file reference."""
        
        if link_target not in self.actual_files:
            self.audit_results["broken_references"].append({
                "source_file": source_file,
                "link_text": link_text,
                "broken_target": link_target,
                "issue": "file_not_found"
            })
        else:
            self.audit_results["correct_references"] += 1
    
    def _matches_naming_convention(self, filename: str) -> bool:
        """Check if filename matches expected naming conventions."""
        
        # Special case for Phase 6.5
        if filename == "phase_06_5_feature_extraction.md":
            return True
        
        # Regular phase files
        if re.match(self.expected_patterns["phase_files"], filename):
            return True
        
        # README file
        if filename == "README.md":
            return True
        
        return False
    
    def _validate_link_text_format(self, link_text: str, target_file: str) -> bool:
        """Validate that link text follows expected format."""

        # Extract phase number from filename
        phase_match = re.search(r'phase_(\d+(?:_5)?)', target_file)
        if not phase_match:
            return True  # Not a phase file, skip validation

        phase_num_raw = phase_match.group(1)

        # Handle Phase 6.5 special case
        if phase_num_raw == "06_5":
            expected_phase = "6.5"
        else:
            expected_phase = str(int(phase_num_raw))

        # Check if link text contains the phase number (more flexible matching)
        phase_patterns = [
            f"Phase {expected_phase}",
            f"phase {expected_phase}",
            f"Phase{expected_phase}",
            f"phase{expected_phase}"
        ]

        return any(pattern in link_text for pattern in phase_patterns)
    
    def _check_phase_dependencies(self, file_path: Path, content: str):
        """Check phase dependency references for consistency."""
        
        # Extract phase number from filename
        phase_match = re.search(r'phase_(\d+(?:_5)?)', file_path.name)
        if not phase_match:
            return
        
        current_phase = phase_match.group(1).replace('_', '.')
        
        # Find dependency line
        dep_match = re.search(r'\*\*Dependencies\*\*:\s*(.+)', content)
        if dep_match:
            dep_line = dep_match.group(1)
            
            # Extract referenced phases
            phase_refs = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', dep_line)
            
            for ref_text, ref_target in phase_refs:
                # Validate dependency reference
                if ref_target not in self.actual_files:
                    self.audit_results["phase_dependency_issues"].append({
                        "phase": current_phase,
                        "source_file": file_path.name,
                        "broken_dependency": ref_target,
                        "dependency_text": ref_text
                    })
    
    def _generate_audit_summary(self):
        """Generate comprehensive audit summary."""
        
        total_refs = self.audit_results["total_references"]
        broken_refs = len(self.audit_results["broken_references"])
        correct_refs = self.audit_results["correct_references"]
        
        self.audit_results["summary"] = {
            "total_files_audited": self.audit_results["files_audited"],
            "total_references_found": total_refs,
            "correct_references": correct_refs,
            "broken_references": broken_refs,
            "inconsistent_naming": len(self.audit_results["inconsistent_naming"]),
            "phase_dependency_issues": len(self.audit_results["phase_dependency_issues"]),
            "accuracy_percentage": (correct_refs / total_refs * 100) if total_refs > 0 else 0,
            "issues_found": broken_refs + len(self.audit_results["inconsistent_naming"]) + len(self.audit_results["phase_dependency_issues"])
        }
    
    def _save_audit_results(self):
        """Save audit results to file."""
        
        results_file = self.docs_dir / "file_reference_audit_results.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.audit_results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Audit results saved to: {results_file}")
    
    def print_audit_report(self):
        """Print comprehensive audit report."""
        
        print("\n" + "="*80)
        print("📋 COMPREHENSIVE DOCUMENTATION AUDIT REPORT")
        print("="*80)
        
        summary = self.audit_results["summary"]
        
        print(f"📄 Files Audited: {summary['total_files_audited']}")
        print(f"🔗 Total References: {summary['total_references_found']}")
        print(f"✅ Correct References: {summary['correct_references']}")
        print(f"❌ Broken References: {summary['broken_references']}")
        print(f"⚠️  Naming Issues: {summary['inconsistent_naming']}")
        print(f"🔄 Dependency Issues: {summary['phase_dependency_issues']}")
        print(f"📊 Accuracy: {summary['accuracy_percentage']:.1f}%")
        
        # Print detailed issues
        if self.audit_results["broken_references"]:
            print(f"\n❌ BROKEN REFERENCES ({len(self.audit_results['broken_references'])}):")
            for issue in self.audit_results["broken_references"]:
                print(f"  - {issue['source_file']}: '{issue['link_text']}' → {issue['broken_target']}")
        
        if self.audit_results["inconsistent_naming"]:
            print(f"\n⚠️  NAMING ISSUES ({len(self.audit_results['inconsistent_naming'])}):")
            for issue in self.audit_results["inconsistent_naming"]:
                print(f"  - {issue['source_file']}: {issue['issue']} → {issue['target']}")
        
        if self.audit_results["phase_dependency_issues"]:
            print(f"\n🔄 DEPENDENCY ISSUES ({len(self.audit_results['phase_dependency_issues'])}):")
            for issue in self.audit_results["phase_dependency_issues"]:
                print(f"  - Phase {issue['phase']}: Missing {issue['broken_dependency']}")
        
        if summary['issues_found'] == 0:
            print(f"\n🎉 AUDIT PASSED: No issues found!")
        else:
            print(f"\n⚠️  AUDIT ISSUES: {summary['issues_found']} issues need attention")
        
        print("="*80)

def main():
    """Execute comprehensive documentation audit."""
    
    auditor = DocumentationAuditor()
    
    # Perform audit
    results = auditor.audit_all_documentation()
    
    # Print report
    auditor.print_audit_report()
    
    # Return exit code based on results
    return 0 if results["summary"]["issues_found"] == 0 else 1

if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
