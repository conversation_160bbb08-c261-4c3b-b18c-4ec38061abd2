# 🧩 Phase 5: Tempo Alignment & BPM Validation

**Status**: 📋 Planned  
**Estimated Duration**: 2 days  
**Dependencies**: [Phase 4: Beat Position Estimation](phase_04_beat_estimation.md)  
**Next Phase**: [Phase 6: Note Candidate Window Detection](phase_06_note_candidates.md)

---

## 1. **Phase Purpose**

This phase aligns detected beats with the expected BPM from TJA files and validates tempo consistency across segments. This step is isolated because:

- **Tempo validation** ensures detected beats match musical expectations
- **Alignment correction** fixes systematic timing errors
- **BPM consistency** is critical for accurate chart generation
- **Offset calculation** aligns audio timing with TJA timing reference

**Why Isolated**: Tempo alignment requires different algorithms than beat detection and involves cross-referencing with TJA metadata. The alignment results affect all subsequent note timing calculations.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 4
beat_positions: Dict                # Detected beat positions per segment
onset_positions: Dict               # Detected onset positions
tempo_estimates: List[float]        # Estimated tempos per segment

# From TJA metadata
tja_bpm: float                     # Expected BPM from TJA file
tja_offset: float                  # Timing offset from TJA file (seconds)
tja_measure_info: Dict             # Measure signatures and tempo changes

# Input directory structure
data\\processed\\phase4\\
├── beat_positions\\*.json          # Beat detection results
├── onset_positions\\*.json         # Onset detection results
└── tempo_analysis\\*.json          # Tempo estimation details
```

### **Outputs**
```python
# Tempo alignment results
tempo_alignment: Dict = {
    "aligned_bpm": float,           # Final aligned BPM
    "bpm_confidence": float,        # Confidence in BPM alignment
    "tempo_drift": float,           # Detected tempo variation (%)
    "alignment_offset": float,      # Time offset correction (seconds)
    "beat_grid": [                  # Aligned beat grid
        {
            "beat_time": float,     # Corrected beat time
            "original_time": float, # Original detected time
            "correction": float,    # Applied correction
            "grid_position": int,   # Position in regular grid
            "confidence": float     # Alignment confidence
        }
    ],
    "tempo_changes": [              # Detected tempo changes
        {
            "time": float,          # Change time
            "old_bpm": float,       # Previous BPM
            "new_bpm": float,       # New BPM
            "confidence": float     # Change confidence
        }
    ]
}

# Validation results
bpm_validation: Dict = {
    "tja_bpm": float,              # Expected BPM from TJA
    "detected_bpm": float,         # Average detected BPM
    "bpm_error": float,            # Absolute error in BPM
    "bpm_error_percentage": float, # Relative error percentage
    "validation_passed": bool,     # Whether validation passed
    "validation_threshold": float, # Error threshold used
    "segment_consistency": float   # BPM consistency across segments
}

# Aligned beats data (for Phase 6 compatibility)
aligned_beats: List[Dict] = [
    {
        "beat_id": int,                 # Sequential beat identifier
        "beat_time": float,             # Corrected beat time (seconds)
        "original_time": float,         # Original detected time
        "correction": float,            # Applied correction (seconds)
        "grid_position": int,           # Position in regular grid
        "confidence": float,            # Alignment confidence (0-1)
        "beat_strength": float,         # Beat detection strength
        "is_downbeat": bool,            # Whether this is a downbeat
        "measure_position": int,        # Position within measure (0-3 for 4/4)
        "bpm_at_beat": float           # Local BPM at this beat
    }
]

# Output directory structure (Windows-compatible paths)
data\\processed\\phase5\\
├── tempo_alignment\\               # Alignment results per song
│   ├── {filename}.json            # Complete tempo_alignment Dict
│   └── ...
├── bpm_validation\\                # BPM validation results
│   ├── {filename}.json            # Complete bpm_validation Dict
│   └── ...
├── aligned_beats\\                 # Corrected beat positions
│   ├── {filename}.json            # List of aligned_beats Dicts
│   └── ...
├── timing_analysis\\               # Detailed timing analysis
│   ├── {filename}.json            # Timing analysis data
│   └── ...
└── alignment_report.json          # Overall alignment statistics
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import librosa
import numpy as np
import scipy.optimize
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.linear_model import RANSACRegressor
```

### **Core Tempo Alignment Function**
```python
def align_tempo_with_tja(
    beat_positions: List[Dict],
    tja_bpm: float,
    tja_offset: float = 0.0,
    tolerance: float = 0.05  # 5% BPM tolerance
) -> Dict:
    """
    Align detected beats with expected TJA tempo.
    
    Args:
        beat_positions: List of detected beat positions
        tja_bpm: Expected BPM from TJA file
        tja_offset: Timing offset from TJA file
        tolerance: Acceptable BPM error percentage
        
    Returns:
        Dictionary with alignment results
    """
    
    if not beat_positions or tja_bpm <= 0:
        return create_empty_alignment_result()
    
    # Extract beat times
    beat_times = np.array([beat["time"] for beat in beat_positions])
    beat_confidences = np.array([beat["confidence"] for beat in beat_positions])
    
    # Calculate expected beat interval from TJA BPM
    expected_interval = 60.0 / tja_bpm
    
    # 1. Estimate actual BPM from detected beats
    if len(beat_times) > 1:
        beat_intervals = np.diff(beat_times)
        # Use weighted average based on confidence
        weights = (beat_confidences[:-1] + beat_confidences[1:]) / 2
        detected_bpm = 60.0 / np.average(beat_intervals, weights=weights)
    else:
        detected_bpm = tja_bpm
    
    # 2. Check if alignment is needed
    bpm_error = abs(detected_bpm - tja_bpm) / tja_bpm
    
    if bmp_error <= tolerance:
        # Beats are already well-aligned
        alignment_result = {
            "aligned_bpm": detected_bpm,
            "bmp_confidence": np.mean(beat_confidences),
            "tempo_drift": 0.0,
            "alignment_offset": 0.0,
            "beat_grid": create_beat_grid_from_detections(beat_positions),
            "tempo_changes": []
        }
    else:
        # Need to align beats to TJA tempo
        alignment_result = perform_tempo_alignment(
            beat_times, beat_confidences, tja_bpm, tja_offset
        )
    
    return alignment_result

def perform_tempo_alignment(
    beat_times: np.ndarray,
    beat_confidences: np.ndarray,
    target_bpm: float,
    target_offset: float
) -> Dict:
    """
    Perform tempo alignment using optimization.
    
    Args:
        beat_times: Detected beat times
        beat_confidences: Beat detection confidences
        target_bpm: Target BPM from TJA
        target_offset: Target offset from TJA
        
    Returns:
        Alignment results dictionary
    """
    
    target_interval = 60.0 / target_bpm
    
    # 1. Find optimal phase alignment
    # Try different phase offsets to find best alignment
    def alignment_cost(phase_offset):
        """Cost function for phase alignment."""
        # Generate expected beat grid
        max_time = np.max(beat_times)
        expected_beats = np.arange(target_offset + phase_offset, max_time, target_interval)
        
        # Find closest expected beat for each detected beat
        costs = []
        for beat_time, confidence in zip(beat_times, beat_confidences):
            if len(expected_beats) > 0:
                distances = np.abs(expected_beats - beat_time)
                min_distance = np.min(distances)
                # Weight by confidence and penalize large distances
                cost = min_distance * (2.0 - confidence)
                costs.append(cost)
        
        return np.mean(costs) if costs else float('inf')
    
    # Optimize phase offset
    phase_range = np.linspace(0, target_interval, 50)
    phase_costs = [alignment_cost(phase) for phase in phase_range]
    optimal_phase = phase_range[np.argmin(phase_costs)]
    
    # 2. Generate aligned beat grid
    max_time = np.max(beat_times) + target_interval
    aligned_beat_times = np.arange(target_offset + optimal_phase, max_time, target_interval)
    
    # 3. Match detected beats to aligned grid
    beat_grid = []
    for i, detected_time in enumerate(beat_times):
        # Find closest aligned beat
        if len(aligned_beat_times) > 0:
            distances = np.abs(aligned_beat_times - detected_time)
            closest_idx = np.argmin(distances)
            aligned_time = aligned_beat_times[closest_idx]
            correction = aligned_time - detected_time
            
            beat_grid.append({
                "beat_time": float(aligned_time),
                "original_time": float(detected_time),
                "correction": float(correction),
                "grid_position": int(closest_idx),
                "confidence": float(beat_confidences[i])
            })
    
    # 4. Detect tempo changes
    tempo_changes = detect_tempo_changes(beat_times, target_bpm)
    
    # 5. Calculate tempo drift
    if len(beat_times) > 1:
        actual_intervals = np.diff(beat_times)
        tempo_drift = (np.std(actual_intervals) / np.mean(actual_intervals)) * 100
    else:
        tempo_drift = 0.0
    
    return {
        "aligned_bpm": target_bpm,
        "bpm_confidence": 1.0 - min(phase_costs) / target_interval,
        "tempo_drift": float(tempo_drift),
        "alignment_offset": float(optimal_phase),
        "beat_grid": beat_grid,
        "tempo_changes": tempo_changes
    }

def detect_tempo_changes(beat_times: np.ndarray, base_bpm: float, window_size: int = 8) -> List[Dict]:
    """
    Detect tempo changes using sliding window analysis.
    
    Args:
        beat_times: Array of beat times
        base_bpm: Base tempo for comparison
        window_size: Number of beats in analysis window
        
    Returns:
        List of detected tempo changes
    """
    
    if len(beat_times) < window_size * 2:
        return []
    
    tempo_changes = []
    base_interval = 60.0 / base_bpm
    
    for i in range(window_size, len(beat_times) - window_size):
        # Calculate local tempo
        window_start = i - window_size // 2
        window_end = i + window_size // 2
        
        window_times = beat_times[window_start:window_end]
        if len(window_times) > 1:
            local_intervals = np.diff(window_times)
            local_bpm = 60.0 / np.mean(local_intervals)
            
            # Check for significant tempo change
            bpm_change = abs(local_bpm - base_bpm) / base_bpm
            if bmp_change > 0.1:  # 10% change threshold
                tempo_changes.append({
                    "time": float(beat_times[i]),
                    "old_bpm": float(base_bpm),
                    "new_bpm": float(local_bpm),
                    "confidence": float(1.0 - bmp_change)  # Lower confidence for larger changes
                })
                base_bpm = local_bpm  # Update base for next comparison
    
    return tempo_changes
```

### **BPM Validation Function**
```python
def validate_bpm_alignment(
    detected_bpm: float,
    tja_bpm: float,
    segment_bpms: List[float],
    validation_threshold: float = 0.05
) -> Dict:
    """
    Validate BPM alignment against TJA reference.
    
    Args:
        detected_bpm: Average detected BPM
        tja_bpm: Expected BPM from TJA
        segment_bpms: BPM estimates from all segments
        validation_threshold: Maximum acceptable error percentage
        
    Returns:
        Validation results dictionary
    """
    
    # Calculate BPM error
    bpm_error = abs(detected_bpm - tja_bpm)
    bpm_error_percentage = bpm_error / tja_bpm * 100
    
    # Check validation
    validation_passed = bmp_error_percentage <= (validation_threshold * 100)
    
    # Calculate segment consistency
    if len(segment_bpms) > 1:
        segment_consistency = 1.0 - (np.std(segment_bpms) / np.mean(segment_bpms))
    else:
        segment_consistency = 1.0
    
    return {
        "tja_bpm": float(tja_bpm),
        "detected_bpm": float(detected_bpm),
        "bpm_error": float(bpm_error),
        "bpm_error_percentage": float(bmp_error_percentage),
        "validation_passed": validation_passed,
        "validation_threshold": float(validation_threshold * 100),
        "segment_consistency": float(max(0.0, segment_consistency))
    }
```

### **Batch Processing Pipeline**
```python
def process_tempo_alignment(
    input_dir: Path = Path("data\\processed\\phase4"),
    output_dir: Path = Path("data\\processed\\phase5"),
    tja_data_dir: Path = Path("data\\raw\\ese"),
    bpm_tolerance: float = 0.05
) -> Dict:
    """Process tempo alignment for entire dataset."""
    
    # Setup output directories
    (output_dir / "tempo_alignment").mkdir(parents=True, exist_ok=True)
    (output_dir / "bpm_validation").mkdir(parents=True, exist_ok=True)
    (output_dir / "aligned_beats").mkdir(parents=True, exist_ok=True)
    (output_dir / "timing_analysis").mkdir(parents=True, exist_ok=True)
    
    # Find all beat position files
    beat_files = list((input_dir / "beat_positions").glob("*.json"))
    
    results = {
        "total_songs": 0,
        "processed_songs": 0,
        "validation_passed": 0,
        "validation_failed": 0,
        "avg_bpm_error": 0.0,
        "avg_tempo_drift": 0.0,
        "processing_errors": []
    }
    
    all_bpm_errors = []
    all_tempo_drifts = []
    song_results = {}
    
    # Group beat files by song
    songs = {}
    for beat_file in beat_files:
        song_name = beat_file.stem.split("_segment_")[0]
        if song_name not in songs:
            songs[song_name] = []
        songs[song_name].append(beat_file)
    
    results["total_songs"] = len(songs)
    
    for song_name, song_beat_files in tqdm(songs.items(), desc="Aligning tempo"):
        try:
            # Load TJA metadata
            tja_bpm, tja_offset = load_tja_metadata(song_name, tja_data_dir)
            
            if tja_bpm is None:
                logging.warning(f"No TJA metadata found for {song_name}")
                continue
            
            # Load all beat positions for this song
            all_beat_positions = []
            segment_bpms = []
            
            for beat_file in song_beat_files:
                with open(beat_file, 'r') as f:
                    beat_data = json.load(f)
                
                if beat_data.get("beats"):
                    all_beat_positions.extend(beat_data["beats"])
                    if beat_data.get("tempo", 0) > 0:
                        segment_bpms.append(beat_data["tempo"])
            
            if not all_beat_positions:
                continue
            
            # Perform tempo alignment
            alignment_result = align_tempo_with_tja(
                all_beat_positions, tja_bpm, tja_offset, bpm_tolerance
            )
            
            # Validate BPM alignment
            detected_bpm = np.mean(segment_bpms) if segment_bpms else tja_bpm
            validation_result = validate_bpm_alignment(
                detected_bpm, tja_bpm, segment_bpms, bpm_tolerance
            )
            
            # Save results
            alignment_file = output_dir / "tempo_alignment" / f"{song_name}.json"
            with open(alignment_file, 'w') as f:
                json.dump(alignment_result, f, indent=2)
            
            validation_file = output_dir / "bpm_validation" / f"{song_name}.json"
            with open(validation_file, 'w') as f:
                json.dump(validation_result, f, indent=2)
            
            # Save aligned beats
            aligned_beats_file = output_dir / "aligned_beats" / f"{song_name}.json"
            with open(aligned_beats_file, 'w') as f:
                json.dump(alignment_result["beat_grid"], f, indent=2)
            
            # Update statistics
            results["processed_songs"] += 1
            
            if validation_result["validation_passed"]:
                results["validation_passed"] += 1
            else:
                results["validation_failed"] += 1
            
            all_bmp_errors.append(validation_result["bmp_error_percentage"])
            all_tempo_drifts.append(alignment_result["tempo_drift"])
            
            song_results[song_name] = {
                "alignment": alignment_result,
                "validation": validation_result
            }
            
        except Exception as e:
            error_info = {"song": song_name, "error": str(e)}
            results["processing_errors"].append(error_info)
            logging.error(f"Error processing {song_name}: {e}")
    
    # Calculate final statistics
    if all_bmp_errors:
        results["avg_bmp_error"] = float(np.mean(all_bmp_errors))
    if all_tempo_drifts:
        results["avg_tempo_drift"] = float(np.mean(all_tempo_drifts))
    
    # Save comprehensive results
    with open(output_dir / "alignment_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

def load_tja_metadata(song_name: str, tja_data_dir: Path) -> Tuple[Optional[float], Optional[float]]:
    """Load BPM and offset from TJA file."""
    try:
        # Find TJA file for this song
        tja_files = list(tja_data_dir.rglob(f"{song_name}.tja"))
        
        if not tja_files:
            return None, None
        
        tja_file = tja_files[0]
        
        # Parse TJA file for BPM and OFFSET
        with open(tja_file, 'r', encoding='utf-8-sig') as f:
            content = f.read()
        
        bpm = None
        offset = None
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('BPM:'):
                bpm = float(line.split(':')[1])
            elif line.startswith('OFFSET:'):
                offset = float(line.split(':')[1])
        
        return bpm, offset or 0.0
        
    except Exception as e:
        logging.error(f"Error loading TJA metadata for {song_name}: {e}")
        return None, None
```

---

## 4. **Best Practices**

### **Robust Alignment**
- Use multiple alignment methods (phase, tempo, offset)
- Weight alignment by beat detection confidence
- Handle tempo changes gracefully
- Validate alignment results against musical expectations

### **Error Tolerance**
- Set reasonable BPM tolerance (±5% typical)
- Allow for human performance variations
- Consider genre-specific tempo characteristics
- Implement fallback strategies for poor alignment

### **Optimization Strategies**
```python
# Use RANSAC for robust tempo estimation
from sklearn.linear_model import RANSACRegressor

def robust_tempo_estimation(beat_times: np.ndarray) -> float:
    """Estimate tempo robustly using RANSAC."""
    if len(beat_times) < 4:
        return 120.0  # Default BPM
    
    # Prepare data for linear regression
    X = beat_times[:-1].reshape(-1, 1)
    y = np.diff(beat_times)
    
    # Use RANSAC to handle outliers
    ransac = RANSACRegressor(random_state=42)
    ransac.fit(X, y)
    
    # Convert interval to BPM
    estimated_interval = np.mean(ransac.predict(X))
    return 60.0 / estimated_interval if estimated_interval > 0 else 120.0
```

---

## 5. **Challenges & Pitfalls**

### **Tempo Variations**
- **Issue**: Songs with tempo changes, rubato, accelerando
- **Example**: Classical music, live performances
- **Mitigation**: Use segment-based alignment, detect tempo changes
- **Solution**: Implement adaptive tempo tracking

### **Phase Ambiguity**
- **Issue**: Beats detected at wrong phase (e.g., off-beats detected as beats)
- **Symptoms**: Consistent timing offset, poor alignment
- **Mitigation**: Use onset information to validate beat phases
- **Solution**: Multi-phase alignment testing

### **BPM Harmonics**
- **Issue**: Detected BPM is double or half of actual BPM
- **Example**: Detecting 240 BPM instead of 120 BPM
- **Mitigation**: Check for harmonic relationships with expected BPM
- **Solution**: Implement BPM harmonic correction

### **Offset Calculation**
- **Issue**: Systematic timing offset between audio and TJA reference
- **Causes**: Processing delays, different timing references
- **Mitigation**: Use multiple reference points for offset calculation
- **Solution**: Implement cross-correlation-based offset detection

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 4 Complete**: Beat positions and tempo estimates
- **Required Files**:
  - `data\\processed\\phase4\\beat_positions\\*.json`
  - `data\\raw\\ese\\**\\*.tja` (for BPM reference)
- **Libraries**: `scipy`, `sklearn` for optimization and robust estimation

### **What This Phase Unlocks**
- **Phase 6**: Aligned beat grid enables accurate note candidate detection
- **Phase 7**: Tempo-corrected timing improves note classification
- **Phase 10**: Validated BPM is essential for measure segmentation
- **All Training Phases**: Accurate timing alignment improves model performance

### **Output Dependencies**
Subsequent phases depend on these Phase 5 outputs:
- `data\\processed\\phase5\\aligned_beats\\*.json` - Tempo-corrected beat positions
- `data\\processed\\phase5\\bpm_validation\\*.json` - BPM validation results
- `data\\processed\\phase5\\tempo_alignment\\*.json` - Comprehensive alignment data

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_tempo_alignment():
    """Test tempo alignment with known BPM."""
    # Create synthetic beat positions with slight timing errors
    true_bpm = 120.0
    true_interval = 60.0 / true_bpm
    
    # Add small random errors to beat times
    perfect_beats = np.arange(0, 8, true_interval)
    noisy_beats = perfect_beats + np.random.normal(0, 0.02, len(perfect_beats))
    
    beat_positions = [
        {"time": t, "confidence": 0.8} for t in noisy_beats
    ]
    
    # Test alignment
    result = align_tempo_with_tja(beat_positions, true_bpm, 0.0)
    
    assert abs(result["aligned_bpm"] - true_bpm) < 1.0
    assert result["bpm_confidence"] > 0.7
    assert len(result["beat_grid"]) == len(beat_positions)

def test_bpm_validation():
    """Test BPM validation functionality."""
    detected_bpm = 122.5
    tja_bpm = 120.0
    segment_bpms = [121.0, 122.0, 123.0, 122.5]
    
    result = validate_bpm_alignment(detected_bpm, tja_bpm, segment_bpms, 0.05)
    
    assert result["bmp_error_percentage"] < 5.0
    assert result["validation_passed"] == True
    assert result["segment_consistency"] > 0.9
```

### **Quality Metrics**
- **BPM Accuracy**: >90% of songs within ±5 BPM of TJA reference
- **Alignment Precision**: Beat positions within ±50ms after alignment
- **Tempo Consistency**: <5% tempo drift within songs
- **Validation Pass Rate**: >85% of songs pass BPM validation

### **Visual Validation**
```python
def visualize_tempo_alignment(
    original_beats: List[float],
    aligned_beats: List[float],
    tja_bpm: float,
    output_path: Path
):
    """Visualize tempo alignment results."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8))
    
    # Plot original vs aligned beats
    ax1.scatter(original_beats, [1]*len(original_beats), 
               alpha=0.7, color='red', label='Original Beats')
    ax1.scatter(aligned_beats, [0.5]*len(aligned_beats), 
               alpha=0.7, color='blue', label='Aligned Beats')
    
    # Show expected beat grid
    if aligned_beats:
        expected_interval = 60.0 / tja_bpm
        expected_beats = np.arange(0, max(aligned_beats) + expected_interval, expected_interval)
        ax1.scatter(expected_beats, [0]*len(expected_beats), 
                   alpha=0.5, color='green', marker='|', s=100, label='Expected Grid')
    
    ax1.set_title(f'Beat Alignment (Target BPM: {tja_bpm})')
    ax1.set_xlabel('Time (seconds)')
    ax1.set_yticks([0, 0.5, 1])
    ax1.set_yticklabels(['Expected', 'Aligned', 'Original'])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot alignment corrections
    if len(original_beats) == len(aligned_beats):
        corrections = np.array(aligned_beats) - np.array(original_beats)
        ax2.plot(original_beats, corrections * 1000, 'o-', color='purple')
        ax2.axhline(0, color='black', linestyle='--', alpha=0.5)
        ax2.set_title('Timing Corrections Applied')
        ax2.set_xlabel('Time (seconds)')
        ax2.set_ylabel('Correction (ms)')
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
```

### **Example Success Case**
```python
# Expected output for successful tempo alignment
alignment_result = align_tempo_with_tja(detected_beats, 128.0, -0.5)

# Expected aligned_beats format (for Phase 6 compatibility):
aligned_beats = [
    {
        "beat_id": 0,
        "beat_time": 0.445,
        "original_time": 0.468,
        "correction": -0.023,
        "grid_position": 0,
        "confidence": 0.85,
        "beat_strength": 0.92,
        "is_downbeat": True,
        "measure_position": 0,
        "bpm_at_beat": 128.0
    },
    {
        "beat_id": 1,
        "beat_time": 0.914,
        "original_time": 0.937,
        "correction": -0.023,
        "grid_position": 1,
        "confidence": 0.85,
        "beat_strength": 0.78,
        "is_downbeat": False,
        "measure_position": 1,
        "bpm_at_beat": 128.0
    }
]

# Expected file outputs:
# data\\processed\\phase5\\aligned_beats\\Lemon.json - List of aligned_beats Dicts
# data\\processed\\phase5\\tempo_alignment\\Lemon.json - Complete tempo_alignment Dict
# data\\processed\\phase5\\bpm_validation\\Lemon.json - Complete bmp_validation Dict
```

---

**Phase 5 Complete**. This phase ensures accurate tempo alignment between detected beats and TJA references, providing the precise timing foundation needed for note generation.

**Next**: [Phase 6: Note Candidate Window Detection](phase_06_note_candidates.md)
