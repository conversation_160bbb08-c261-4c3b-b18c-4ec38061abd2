"""
Batch execution script for Phase 1: Audio Loading & Format Standardization
Processes the dataset in manageable chunks to avoid memory issues.
"""

import os
import sys
import time
from pathlib import Path

def main():
    """Main execution function for batch processing."""
    print("🎼 TJA Chart Generation - Phase 1 Batch Processing")
    print("=" * 60)
    
    # Add src to Python path
    src_path = Path(__file__).parent.parent / "src"
    sys.path.insert(0, str(src_path))
    
    try:
        from phases.phase_01_audio_loading import Phase1AudioProcessor
        
        # Initialize processor
        processor = Phase1AudioProcessor()
        
        # Get all audio files
        print("📁 Scanning for audio files...")
        audio_files = processor.get_audio_files_list(validation_mode=False)
        total_files = len(audio_files)
        
        print(f"Found {total_files} audio files to process")
        
        # Process in chunks of 500 files
        chunk_size = 500
        chunks = [audio_files[i:i + chunk_size] for i in range(0, len(audio_files), chunk_size)]
        
        print(f"Processing in {len(chunks)} chunks of {chunk_size} files each")
        
        total_successful = 0
        total_failed = 0
        
        for chunk_idx, chunk in enumerate(chunks, 1):
            print(f"\n🚀 Processing chunk {chunk_idx}/{len(chunks)} ({len(chunk)} files)...")
            
            # Process chunk
            chunk_results = []
            for file_path in chunk:
                success, metadata = processor.process_single_file(file_path)
                chunk_results.append((success, metadata))
                
                if success:
                    total_successful += 1
                else:
                    total_failed += 1
                    processor.stats['errors'].append({
                        'file': str(file_path),
                        'error': metadata.get('error_message', 'Unknown error')
                    })
            
            # Update statistics
            processor.stats['processed_files'] += len(chunk)
            processor.stats['successful_files'] = total_successful
            processor.stats['failed_files'] = total_failed
            
            success_rate = total_successful / (total_successful + total_failed) if (total_successful + total_failed) > 0 else 0
            
            print(f"Chunk {chunk_idx} complete: {len([r for r in chunk_results if r[0]])}/{len(chunk)} successful")
            print(f"Overall progress: {total_successful + total_failed}/{total_files} files ({success_rate:.1%} success rate)")
            
            # Force garbage collection between chunks
            processor.memory_manager.force_garbage_collection()
            
            # Brief pause between chunks
            time.sleep(1)
        
        # Final statistics
        final_success_rate = total_successful / total_files if total_files > 0 else 0
        
        print(f"\n📊 Final Results:")
        print(f"   Total files: {total_files}")
        print(f"   Successful: {total_successful} ({final_success_rate:.1%})")
        print(f"   Failed: {total_failed}")
        
        # Save summary
        processor.stats['total_files'] = total_files
        processor.save_processing_summary()
        
        # Validate outputs
        validation_results = processor.validate_outputs()
        print(f"   Audio files created: {validation_results['audio_files_created']}")
        print(f"   Metadata files created: {validation_results['metadata_files_created']}")
        
        # Check quality gates
        min_success_rate = processor.config['quality']['min_success_rate']
        if final_success_rate >= min_success_rate:
            print(f"✅ Quality gate passed: {final_success_rate:.1%} >= {min_success_rate:.1%}")
            return True
        else:
            print(f"❌ Quality gate failed: {final_success_rate:.1%} < {min_success_rate:.1%}")
            return False
            
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
